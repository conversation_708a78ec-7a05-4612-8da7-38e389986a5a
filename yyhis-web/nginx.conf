server {
    listen 80;
    server_name localhost;
    
    # 静态文件目录
    root /usr/share/nginx/html;
    index index.html;
    
    # 处理SPA应用的路由
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API反向代理配置
            location /api/ { ## 后端项目 - 管理后台
            proxy_pass http://*************:6596/; ## 重要！！！proxy_pass 需要设置为后端项目所在服务器的 IP
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

    
    # 禁止访问.htaccess文件
    location ~ /\.ht {
        deny all;
    }
    
    # 错误页面
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
